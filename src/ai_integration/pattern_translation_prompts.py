#!/usr/bin/env python3
"""
🎯 PATTERN TRANSLATION PROMPTS - STAGE 2 OF TWO-STAGE DISCOVERY SYSTEM

This module implements Stage 2: Translation of sophisticated patterns discovered in Stage 1
into backtesting-compatible format that can be parsed and validated by the existing pipeline.

Stage 1: Tom <PERSON> → Stage 2: Backtesting Translation → Validation Pipeline
"""

class PatternTranslationPrompts:
    """Generate prompts to translate sophisticated patterns into backtesting-compatible format"""
    
    @staticmethod
    def get_backtesting_constraints():
        """Get the exact constraints required for backtesting compatibility"""
        return {
            "entry_logic_formats": [
                "current_close > previous_high",
                "current_close < previous_low", 
                "current_close > previous_close",
                "current_close < previous_close",
                "current_high > previous_high",
                "current_low < previous_low"
            ],
            "stop_logic_formats": [
                "previous_low",
                "previous_high", 
                "previous_close"
            ],
            "target_logic_formats": [
                "entry_price + (entry_price - stop_price) * X.X",
                "entry_price - (stop_price - entry_price) * X.X"
            ],
            "direction_options": ["long", "short"],
            "position_size_format": "numeric value (e.g., 1.0, 0.5, 2.0)",
            "timeframe_options": ["5min", "15min", "30min", "1h", "4h", "1d"]
        }
    
    @staticmethod
    def get_translation_principles():
        """Core principles for translating sophisticated patterns to simple rules"""
        return [
            "Preserve the core behavioral insight while simplifying execution logic",
            "Convert complex multi-condition patterns into the most essential trigger condition",
            "Maintain the statistical edge by focusing on the primary pattern driver",
            "Ensure the simplified pattern still exploits the same behavioral inefficiency",
            "Choose the backtesting format that best captures the pattern's essence",
            "Prioritize patterns that can be effectively simplified without losing their edge",
            "If a pattern cannot be simplified without losing its core logic, note this limitation"
        ]
    
    @staticmethod
    def get_translation_examples():
        """Examples of how to translate sophisticated patterns to backtesting format"""
        return [
            {
                "sophisticated": "When volatility contracts after 3 consecutive higher closes, and the 4th day opens below previous close but recovers above the 2nd day's high within 2 hours, participants typically drive price to test recent highs",
                "core_insight": "Volatility contraction followed by recovery above key level triggers momentum continuation",
                "simplified_trigger": "current_close > previous_high (captures the recovery above key level)",
                "direction": "long",
                "stop": "previous_low",
                "target": "entry_price + (entry_price - stop_price) * 2.0"
            },
            {
                "sophisticated": "During London-NY overlap, when institutional flows create specific price behaviors where the market makes a new session high but fails to hold above the previous day's close, retail participants typically panic-sell",
                "core_insight": "Failed breakout above previous close during high-volume session triggers retail panic",
                "simplified_trigger": "current_close < previous_close (captures the failure to hold)",
                "direction": "short", 
                "stop": "previous_high",
                "target": "entry_price - (stop_price - entry_price) * 1.5"
            }
        ]
    
    @staticmethod
    def generate_stage2_translation_prompt(sophisticated_patterns):
        """
        Generate Stage 2: Pattern Translation Prompt
        
        This prompt takes sophisticated patterns from Stage 1 and translates them
        into backtesting-compatible format while preserving their core insights.
        
        Args:
            sophisticated_patterns: The output from Stage 1 discovery
            
        Returns:
            Stage 2 translation prompt string
        """
        
        constraints = PatternTranslationPrompts.get_backtesting_constraints()
        principles = PatternTranslationPrompts.get_translation_principles()
        examples = PatternTranslationPrompts.get_translation_examples()
        
        prompt = f"""🎯 STAGE 2: PATTERN TRANSLATION TO BACKTESTING FORMAT

You have successfully discovered sophisticated trading patterns using Tom Hougaard's methodology. Now you must translate these patterns into backtesting-compatible format while preserving their core behavioral insights.

📊 SOPHISTICATED PATTERNS DISCOVERED IN STAGE 1:
{sophisticated_patterns}

🔧 TRANSLATION MISSION:
Convert each sophisticated pattern above into simple backtesting-compatible rules that can be tested and validated. Your goal is to preserve the core behavioral insight while simplifying the execution logic.

🎯 TRANSLATION PRINCIPLES:
{chr(10).join([f'• {principle}' for principle in principles])}

🚨 BACKTESTING CONSTRAINTS - MANDATORY FORMAT:

**Entry Logic - Use ONLY these exact formats:**
{chr(10).join([f'• {fmt}' for fmt in constraints['entry_logic_formats']])}

**Stop Logic - Use ONLY these exact formats:**
{chr(10).join([f'• {fmt}' for fmt in constraints['stop_logic_formats']])}

**Target Logic - Use ONLY these exact formats:**
{chr(10).join([f'• {fmt}' for fmt in constraints['target_logic_formats']])}

**Direction:** {', '.join(constraints['direction_options'])}
**Position Size:** {constraints['position_size_format']}
**Timeframe:** {', '.join(constraints['timeframe_options'])}

🌟 TRANSLATION EXAMPLES:

Example 1:
Sophisticated Pattern: "{examples[0]['sophisticated']}"
Core Behavioral Insight: {examples[0]['core_insight']}
Translated Entry Logic: {examples[0]['simplified_trigger']}
Direction: {examples[0]['direction']}
Stop Logic: {examples[0]['stop']}
Target Logic: {examples[0]['target']}

Example 2:
Sophisticated Pattern: "{examples[1]['sophisticated']}"
Core Behavioral Insight: {examples[1]['core_insight']}
Translated Entry Logic: {examples[1]['simplified_trigger']}
Direction: {examples[1]['direction']}
Stop Logic: {examples[1]['stop']}
Target Logic: {examples[1]['target']}

🎯 REQUIRED TRANSLATION OUTPUT FORMAT:

For each sophisticated pattern from Stage 1, provide:

**PATTERN [X]: [Same Name from Stage 1]**
Core Behavioral Insight: [The essential behavioral inefficiency this pattern exploits]
Translation Logic: [Explain how you simplified the complex pattern while preserving its edge]
Entry Logic: [EXACT format from approved list above]
Direction: [long or short]
Stop Logic: [EXACT format from approved list above]
Target Logic: [EXACT format from approved list above]
Position Size: [numeric value]
Timeframe: [from approved list above]
Preserved Edge: [Confirm the simplified pattern still exploits the same behavioral inefficiency]

🚨 CRITICAL TRANSLATION REQUIREMENTS:

• Use ONLY the exact entry/stop/target formats listed above
• NO descriptive text in logic fields - only the exact syntax
• Preserve the core behavioral insight in the simplified pattern
• Ensure each translated pattern still has a clear statistical edge
• Choose the backtesting format that best captures the pattern's essence
• If a pattern cannot be effectively simplified, explain why and suggest alternatives

🎯 PROFITABILITY REQUIREMENTS:
• Profit target MUST be at least 2:1 risk-reward minimum (preferably 3:1)
• Pattern must overcome 1-pip spread cost PLUS generate substantial profits
• Focus ONLY on HIGH-PROBABILITY setups with clear statistical edges
• Expected win rate >60% OR risk-reward ratio >2:1

Begin translating your sophisticated patterns into backtesting-compatible format now:"""

        return prompt
    
    @staticmethod
    def validate_translation_output(translation_output):
        """
        Validate that the translation output meets backtesting requirements
        
        Args:
            translation_output: The LLM's translation output
            
        Returns:
            Dict with validation results
        """
        constraints = PatternTranslationPrompts.get_backtesting_constraints()
        
        validation_results = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Check for required entry logic formats
        valid_entry_found = False
        for entry_format in constraints['entry_logic_formats']:
            if entry_format in translation_output.lower():
                valid_entry_found = True
                break
        
        if not valid_entry_found:
            validation_results['valid'] = False
            validation_results['errors'].append("No valid entry logic format found")
        
        # Check for required stop logic formats
        valid_stop_found = False
        for stop_format in constraints['stop_logic_formats']:
            if stop_format in translation_output.lower():
                valid_stop_found = True
                break
        
        if not valid_stop_found:
            validation_results['valid'] = False
            validation_results['errors'].append("No valid stop logic format found")
        
        # Check for direction
        direction_found = False
        for direction in constraints['direction_options']:
            if f"direction: {direction}" in translation_output.lower():
                direction_found = True
                break
        
        if not direction_found:
            validation_results['warnings'].append("Direction not clearly specified")
        
        return validation_results
